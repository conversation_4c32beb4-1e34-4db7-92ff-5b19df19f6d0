<?php $__env->startSection('content'); ?>
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">مدیریت فروش‌ها</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="<?php echo e(route('sales.create')); ?>" class="btn btn-success btn-lg">
            <i class="fas fa-plus me-2"></i>
            ثبت فروش جدید
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-body">
                <?php if($sales->count() > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>شماره فروش</th>
                                    <th>مشتری</th>
                                    <th>تعداد اقلام</th>
                                    <th>مبلغ کل</th>
                                    <th>تاریخ فروش</th>
                                    <th>عملیات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $sales; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $sale): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td>#<?php echo e($sale->id); ?></td>
                                    <td><?php echo e($sale->customer ? $sale->customer->name : 'مشتری ناشناس'); ?></td>
                                    <td><?php echo e($sale->saleItems->count()); ?> قلم</td>
                                    <td><?php echo e(number_format($sale->total_amount)); ?> تومان</td>
                                    <td><?php echo e($sale->created_at->format('Y/m/d H:i')); ?></td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="<?php echo e(route('sales.show', $sale)); ?>" class="btn btn-sm btn-info">جزئیات</a>
                                            <form action="<?php echo e(route('sales.destroy', $sale)); ?>" method="POST" class="d-inline" onsubmit="return confirm('آیا مطمئن هستید؟ این عمل موجودی محصولات را بازمی‌گرداند.')">
                                                <?php echo csrf_field(); ?>
                                                <?php echo method_field('DELETE'); ?>
                                                <button type="submit" class="btn btn-sm btn-danger">حذف</button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="mt-3">
                        <div class="row">
                            <div class="col-md-6">
                                <strong>تعداد کل فروش‌ها: <?php echo e($sales->count()); ?></strong>
                            </div>
                            <div class="col-md-6 text-end">
                                <strong>مجموع درآمد: <?php echo e(number_format($sales->sum('total_amount'))); ?> تومان</strong>
                            </div>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="text-center py-4">
                        <p class="text-muted">هیچ فروشی ثبت نشده است.</p>
                        <a href="<?php echo e(route('sales.create')); ?>" class="btn btn-primary">ثبت اولین فروش</a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\project monogroph\faiz-ahmad-shop\resources\views/sales/index.blade.php ENDPATH**/ ?>