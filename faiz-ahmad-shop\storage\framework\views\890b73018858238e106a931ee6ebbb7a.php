<?php $__env->startSection('content'); ?>
<div class="row">
    <div class="col-md-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>مدیریت محصولات</h1>
            <a href="<?php echo e(route('products.create')); ?>" class="btn btn-primary">افزودن محصول جدید</a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-body">
                <?php if($products->count() > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>شناسه</th>
                                    <th>نام محصول</th>
                                    <th>قیمت</th>
                                    <th>موجودی</th>
                                    <th>توضیحات</th>
                                    <th>عملیات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td><?php echo e($product->id); ?></td>
                                    <td><?php echo e($product->name); ?></td>
                                    <td><?php echo e(number_format($product->price)); ?> تومان</td>
                                    <td>
                                        <?php if($product->stock <= 5): ?>
                                            <span class="badge bg-danger"><?php echo e($product->stock); ?></span>
                                        <?php elseif($product->stock <= 10): ?>
                                            <span class="badge bg-warning"><?php echo e($product->stock); ?></span>
                                        <?php else: ?>
                                            <span class="badge bg-success"><?php echo e($product->stock); ?></span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo e(Str::limit($product->description, 50)); ?></td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="<?php echo e(route('products.show', $product)); ?>" class="btn btn-sm btn-info">نمایش</a>
                                            <a href="<?php echo e(route('products.edit', $product)); ?>" class="btn btn-sm btn-warning">ویرایش</a>
                                            <form action="<?php echo e(route('products.destroy', $product)); ?>" method="POST" class="d-inline" onsubmit="return confirm('آیا مطمئن هستید؟')">
                                                <?php echo csrf_field(); ?>
                                                <?php echo method_field('DELETE'); ?>
                                                <button type="submit" class="btn btn-sm btn-danger">حذف</button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="text-center py-4">
                        <p class="text-muted">هیچ محصولی یافت نشد.</p>
                        <a href="<?php echo e(route('products.create')); ?>" class="btn btn-primary">افزودن اولین محصول</a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\project monogroph\faiz-ahmad-shop\resources\views/products/index.blade.php ENDPATH**/ ?>