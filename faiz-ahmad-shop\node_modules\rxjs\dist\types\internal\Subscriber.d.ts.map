{"version": 3, "file": "Subscriber.d.ts", "sourceRoot": "", "sources": ["../../../src/internal/Subscriber.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,QAAQ,EAA0B,MAAM,SAAS,CAAC;AAC3D,OAAO,EAAkB,YAAY,EAAE,MAAM,gBAAgB,CAAC;AAQ9D;;;;;;;GAOG;AACH,qBAAa,UAAU,CAAC,CAAC,CAAE,SAAQ,YAAa,YAAW,QAAQ,CAAC,CAAC,CAAC;IACpE;;;;;;;;;;;;;OAaG;IACH,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,IAAI,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,KAAK,IAAI,EAAE,QAAQ,CAAC,EAAE,MAAM,IAAI,GAAG,UAAU,CAAC,CAAC,CAAC;IAIzG,oGAAoG;IACpG,SAAS,CAAC,SAAS,EAAE,OAAO,CAAS;IACrC,oGAAoG;IACpG,SAAS,CAAC,WAAW,EAAE,UAAU,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC;IAEvD;;;OAGG;gBACS,WAAW,CAAC,EAAE,UAAU,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAC,GAAG,CAAC;IAczD;;;;;OAKG;IACH,IAAI,CAAC,KAAK,EAAE,CAAC,GAAG,IAAI;IAQpB;;;;;OAKG;IACH,KAAK,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,IAAI;IAStB;;;;OAIG;IACH,QAAQ,IAAI,IAAI;IAShB,WAAW,IAAI,IAAI;IAQnB,SAAS,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,GAAG,IAAI;IAI/B,SAAS,CAAC,MAAM,CAAC,GAAG,EAAE,GAAG,GAAG,IAAI;IAQhC,SAAS,CAAC,SAAS,IAAI,IAAI;CAO5B;AAwDD,qBAAa,cAAc,CAAC,CAAC,CAAE,SAAQ,UAAU,CAAC,CAAC,CAAC;gBAEhD,cAAc,CAAC,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,CAAC,KAAK,IAAI,CAAC,GAAG,IAAI,EACnE,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,KAAK,IAAI,CAAC,GAAG,IAAI,EAClC,QAAQ,CAAC,EAAE,CAAC,MAAM,IAAI,CAAC,GAAG,IAAI;CAqCjC;AAgCD;;;;GAIG;AACH,eAAO,MAAM,cAAc,EAAE,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG;IAAE,MAAM,EAAE,IAAI,CAAA;CAKpE,CAAC"}