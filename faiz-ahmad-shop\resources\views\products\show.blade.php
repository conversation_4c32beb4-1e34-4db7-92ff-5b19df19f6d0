@extends('layouts.app')

@section('content')
<div class="row">
    <div class="col-md-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>جزئیات محصول: {{ $product->name }}</h1>
            <div>
                <a href="{{ route('products.edit', $product) }}" class="btn btn-warning">ویرایش</a>
                <a href="{{ route('products.index') }}" class="btn btn-secondary">بازگشت به لیست</a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5>اطلاعات محصول</h5>
            </div>
            <div class="card-body">
                <table class="table table-borderless">
                    <tr>
                        <td><strong>شناسه:</strong></td>
                        <td>{{ $product->id }}</td>
                    </tr>
                    <tr>
                        <td><strong>نام محصول:</strong></td>
                        <td>{{ $product->name }}</td>
                    </tr>
                    <tr>
                        <td><strong>قیمت:</strong></td>
                        <td>{{ number_format($product->price) }} تومان</td>
                    </tr>
                    <tr>
                        <td><strong>موجودی:</strong></td>
                        <td>
                            @if($product->stock <= 5)
                                <span class="badge bg-danger">{{ $product->stock }}</span>
                            @elseif($product->stock <= 10)
                                <span class="badge bg-warning">{{ $product->stock }}</span>
                            @else
                                <span class="badge bg-success">{{ $product->stock }}</span>
                            @endif
                        </td>
                    </tr>
                    <tr>
                        <td><strong>توضیحات:</strong></td>
                        <td>{{ $product->description ?: 'توضیحاتی ثبت نشده است.' }}</td>
                    </tr>
                    <tr>
                        <td><strong>تاریخ ایجاد:</strong></td>
                        <td>{{ $product->created_at->format('Y/m/d H:i') }}</td>
                    </tr>
                    <tr>
                        <td><strong>آخرین به‌روزرسانی:</strong></td>
                        <td>{{ $product->updated_at->format('Y/m/d H:i') }}</td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5>عملیات</h5>
            </div>
            <div class="card-body text-center">
                <a href="{{ route('products.edit', $product) }}" class="btn btn-warning btn-block mb-2">ویرایش محصول</a>
                <a href="{{ route('sales.create') }}" class="btn btn-success btn-block mb-2">فروش این محصول</a>
                <form action="{{ route('products.destroy', $product) }}" method="POST" class="d-inline" onsubmit="return confirm('آیا مطمئن هستید؟')">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger btn-block">حذف محصول</button>
                </form>
            </div>
        </div>
        
        @if($product->stock <= 5)
        <div class="card mt-3">
            <div class="card-body">
                <div class="alert alert-warning">
                    <strong>هشدار!</strong> موجودی این محصول کم است.
                </div>
            </div>
        </div>
        @endif
    </div>
</div>
@endsection
