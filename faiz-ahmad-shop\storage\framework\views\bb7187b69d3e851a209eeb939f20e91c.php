<?php $__env->startSection('content'); ?>
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">مدیریت مشتری‌ها</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="<?php echo e(route('customers.create')); ?>" class="btn btn-info btn-lg">
            <i class="fas fa-user-plus me-2"></i>
            افزودن مشتری جدید
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-body">
                <?php if($customers->count() > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>شناسه</th>
                                    <th>نام مشتری</th>
                                    <th>شماره تماس</th>
                                    <th>تعداد خریدها</th>
                                    <th>تاریخ ثبت</th>
                                    <th>عملیات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $customers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $customer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td><?php echo e($customer->id); ?></td>
                                    <td><?php echo e($customer->name); ?></td>
                                    <td><?php echo e($customer->phone ?: 'ثبت نشده'); ?></td>
                                    <td>
                                        <?php if($customer->sales_count > 0): ?>
                                            <span class="badge bg-success"><?php echo e($customer->sales_count); ?> خرید</span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">بدون خرید</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo e($customer->created_at->format('Y/m/d')); ?></td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="<?php echo e(route('customers.show', $customer)); ?>" class="btn btn-sm btn-info">نمایش</a>
                                            <a href="<?php echo e(route('customers.edit', $customer)); ?>" class="btn btn-sm btn-warning">ویرایش</a>
                                            <form action="<?php echo e(route('customers.destroy', $customer)); ?>" method="POST" class="d-inline" onsubmit="return confirm('آیا مطمئن هستید؟')">
                                                <?php echo csrf_field(); ?>
                                                <?php echo method_field('DELETE'); ?>
                                                <button type="submit" class="btn btn-sm btn-danger">حذف</button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="text-center py-4">
                        <p class="text-muted">هیچ مشتری‌ای یافت نشد.</p>
                        <a href="<?php echo e(route('customers.create')); ?>" class="btn btn-primary">افزودن اولین مشتری</a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\project monogroph\faiz-ahmad-shop\resources\views/customers/index.blade.php ENDPATH**/ ?>