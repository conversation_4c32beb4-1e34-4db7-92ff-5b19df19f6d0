@extends('layouts.app')

@section('content')
<div class="row">
    <div class="col-md-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>مدیریت محصولات</h1>
            <a href="{{ route('products.create') }}" class="btn btn-primary">افزودن محصول جدید</a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-body">
                @if($products->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>شناسه</th>
                                    <th>نام محصول</th>
                                    <th>قیمت</th>
                                    <th>موجودی</th>
                                    <th>توضیحات</th>
                                    <th>عملیات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($products as $product)
                                <tr>
                                    <td>{{ $product->id }}</td>
                                    <td>{{ $product->name }}</td>
                                    <td>{{ number_format($product->price) }} تومان</td>
                                    <td>
                                        @if($product->stock <= 5)
                                            <span class="badge bg-danger">{{ $product->stock }}</span>
                                        @elseif($product->stock <= 10)
                                            <span class="badge bg-warning">{{ $product->stock }}</span>
                                        @else
                                            <span class="badge bg-success">{{ $product->stock }}</span>
                                        @endif
                                    </td>
                                    <td>{{ Str::limit($product->description, 50) }}</td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ route('products.show', $product) }}" class="btn btn-sm btn-info">نمایش</a>
                                            <a href="{{ route('products.edit', $product) }}" class="btn btn-sm btn-warning">ویرایش</a>
                                            <form action="{{ route('products.destroy', $product) }}" method="POST" class="d-inline" onsubmit="return confirm('آیا مطمئن هستید؟')">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="btn btn-sm btn-danger">حذف</button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <div class="text-center py-4">
                        <p class="text-muted">هیچ محصولی یافت نشد.</p>
                        <a href="{{ route('products.create') }}" class="btn btn-primary">افزودن اولین محصول</a>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection
