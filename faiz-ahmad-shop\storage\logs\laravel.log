[2025-06-21 09:14:48] local.ERROR: Vite manifest not found at: C:\Users\<USER>\Desktop\project monogroph\faiz-ahmad-shop\public\build/manifest.json (View: C:\Users\<USER>\Desktop\project monogroph\faiz-ahmad-shop\resources\views\layouts\app.blade.php) (View: C:\Users\<USER>\Desktop\project monogroph\faiz-ahmad-shop\resources\views\layouts\app.blade.php) {"exception":"[object] (Illuminate\\View\\ViewException(code: 0): Vite manifest not found at: C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\public\\build/manifest.json (View: C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\resources\\views\\layouts\\app.blade.php) (View: C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\resources\\views\\layouts\\app.blade.php) at C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Vite.php:934)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(59): Illuminate\\View\\Engines\\CompilerEngine->handleViewException(Object(Illuminate\\View\\ViewException), 1)
#1 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\Users\\En.Faizahmad\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\En.Faizahmad\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#4 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#5 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(78): Illuminate\\View\\View->render()
#6 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(34): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#7 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(924): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#8 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(891): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#9 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#10 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#11 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\RedirectIfAuthenticated.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\RedirectIfAuthenticated->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#21 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#28 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#30 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#53 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#54 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#56 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\View\\ViewException(code: 0): Vite manifest not found at: C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\public\\build/manifest.json (View: C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\resources\\views\\layouts\\app.blade.php) at C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Vite.php:934)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(59): Illuminate\\View\\Engines\\CompilerEngine->handleViewException(Object(Illuminate\\Foundation\\ViteManifestNotFoundException), 2)
#1 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\Users\\En.Faizahmad\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\En.Faizahmad\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#4 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#5 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\storage\\framework\\views\\ca05b96c2e72f3e1cab73865fcb4cbe4.php(104): Illuminate\\View\\View->render()
#6 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Users\\\\<USER>\\Users\\En.Faizahmad\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#8 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(57): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\Users\\En.Faizahmad\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\Users\\En.Faizahmad\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\En.Faizahmad\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#12 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#13 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(78): Illuminate\\View\\View->render()
#14 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(34): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#15 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(924): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#16 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(891): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#17 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#18 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\RedirectIfAuthenticated.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\RedirectIfAuthenticated->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#29 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#36 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#38 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#61 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#62 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#63 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#64 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\Foundation\\ViteManifestNotFoundException(code: 0): Vite manifest not found at: C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\public\\build/manifest.json at C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Vite.php:934)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Vite.php(384): Illuminate\\Foundation\\Vite->manifest('build')
#1 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\storage\\framework\\views\\97e03f7ec250738c76544cb8575cd71d.php(20): Illuminate\\Foundation\\Vite->__invoke(Object(Illuminate\\Support\\Collection))
#2 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Users\\\\<USER>\\Users\\En.Faizahmad\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#4 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(57): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\Users\\En.Faizahmad\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\Users\\En.Faizahmad\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\En.Faizahmad\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#8 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#9 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\storage\\framework\\views\\ca05b96c2e72f3e1cab73865fcb4cbe4.php(104): Illuminate\\View\\View->render()
#10 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Users\\\\<USER>\\Users\\En.Faizahmad\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#12 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(57): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\Users\\En.Faizahmad\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\Users\\En.Faizahmad\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\En.Faizahmad\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#16 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#17 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(78): Illuminate\\View\\View->render()
#18 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(34): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#19 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(924): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#20 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(891): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#21 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#22 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\RedirectIfAuthenticated.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\RedirectIfAuthenticated->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#33 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#40 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#42 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#65 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#66 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#67 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#68 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\Users\En.Faizahmad\Desktop\project monogroph\faiz-ahmad-shop\public\build/manifest.json (View: C:\Users\<USER>\Desktop\project monogroph\faiz-ahmad-shop\resources\views\layouts\app.blade.php) (View: C:\Users\<USER>\Desktop\project monogroph\faiz-ahmad-shop\resources\views\layouts\app.blade.php) {"exception":"[object] (Illuminate\\View\\ViewException(code: 0): Vite manifest not found at: C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\public\\build/manifest.json (View: C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\resources\\views\\layouts\\app.blade.php) (View: C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\resources\\views\\layouts\\app.blade.php) at C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Vite.php:934)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(59): Illuminate\\View\\Engines\\CompilerEngine->handleViewException(Object(Illuminate\\View\\ViewException), 1)
#1 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\Users\\En.Faizahmad\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\En.Faizahmad\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#4 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#5 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(78): Illuminate\\View\\View->render()
#6 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(34): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#7 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(924): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#8 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(891): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#9 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#10 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#11 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\RedirectIfAuthenticated.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\RedirectIfAuthenticated->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#21 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#28 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#30 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#53 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#54 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#56 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\View\\ViewException(code: 0): Vite manifest not found at: C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\public\\build/manifest.json (View: C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\resources\\views\\layouts\\app.blade.php) at C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Vite.php:934)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(59): Illuminate\\View\\Engines\\CompilerEngine->handleViewException(Object(Illuminate\\Foundation\\ViteManifestNotFoundException), 2)
#1 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\Users\\En.Faizahmad\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\En.Faizahmad\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#4 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#5 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\storage\\framework\\views\\ca05b96c2e72f3e1cab73865fcb4cbe4.php(104): Illuminate\\View\\View->render()
#6 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Users\\\\<USER>\\Users\\En.Faizahmad\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#8 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(57): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\Users\\En.Faizahmad\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\Users\\En.Faizahmad\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\En.Faizahmad\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#12 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#13 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(78): Illuminate\\View\\View->render()
#14 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(34): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#15 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(924): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#16 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(891): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#17 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#18 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\RedirectIfAuthenticated.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\RedirectIfAuthenticated->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#29 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#36 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#38 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#61 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#62 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#63 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#64 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\Foundation\\ViteManifestNotFoundException(code: 0): Vite manifest not found at: C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\public\\build/manifest.json at C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Vite.php:934)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Vite.php(384): Illuminate\\Foundation\\Vite->manifest('build')
#1 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\storage\\framework\\views\\97e03f7ec250738c76544cb8575cd71d.php(20): Illuminate\\Foundation\\Vite->__invoke(Object(Illuminate\\Support\\Collection))
#2 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Users\\\\<USER>\\Users\\En.Faizahmad\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#4 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(57): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\Users\\En.Faizahmad\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\Users\\En.Faizahmad\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\En.Faizahmad\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#8 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#9 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\storage\\framework\\views\\ca05b96c2e72f3e1cab73865fcb4cbe4.php(104): Illuminate\\View\\View->render()
#10 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Users\\\\<USER>\\Users\\En.Faizahmad\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#12 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(57): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\Users\\En.Faizahmad\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\Users\\En.Faizahmad\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\En.Faizahmad\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#16 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#17 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(78): Illuminate\\View\\View->render()
#18 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(34): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#19 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(924): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#20 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(891): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#21 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#22 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\RedirectIfAuthenticated.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\RedirectIfAuthenticated->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#33 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#40 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#42 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#65 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#66 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#67 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#68 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\Users\En.Faizahmad\Desktop\project monogroph\faiz-ahmad-shop\public\build/manifest.json (View: C:\Users\<USER>\Desktop\project monogroph\faiz-ahmad-shop\resources\views\layouts\app.blade.php) (View: C:\Users\<USER>\Desktop\project monogroph\faiz-ahmad-shop\resources\views\layouts\app.blade.php) {"exception":"[object] (Illuminate\\View\\ViewException(code: 0): Vite manifest not found at: C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\public\\build/manifest.json (View: C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\resources\\views\\layouts\\app.blade.php) (View: C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\resources\\views\\layouts\\app.blade.php) at C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Vite.php:934)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(59): Illuminate\\View\\Engines\\CompilerEngine->handleViewException(Object(Illuminate\\View\\ViewException), 1)
#1 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\Users\\En.Faizahmad\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\En.Faizahmad\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#4 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#5 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(78): Illuminate\\View\\View->render()
#6 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(34): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#7 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(924): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#8 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(891): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#9 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#10 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#11 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\RedirectIfAuthenticated.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\RedirectIfAuthenticated->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#21 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#28 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#30 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#53 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#54 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#56 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\View\\ViewException(code: 0): Vite manifest not found at: C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\public\\build/manifest.json (View: C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\resources\\views\\layouts\\app.blade.php) at C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Vite.php:934)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(59): Illuminate\\View\\Engines\\CompilerEngine->handleViewException(Object(Illuminate\\Foundation\\ViteManifestNotFoundException), 2)
#1 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\Users\\En.Faizahmad\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\En.Faizahmad\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#4 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#5 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\storage\\framework\\views\\ca05b96c2e72f3e1cab73865fcb4cbe4.php(104): Illuminate\\View\\View->render()
#6 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Users\\\\<USER>\\Users\\En.Faizahmad\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#8 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(57): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\Users\\En.Faizahmad\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\Users\\En.Faizahmad\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\En.Faizahmad\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#12 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#13 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(78): Illuminate\\View\\View->render()
#14 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(34): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#15 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(924): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#16 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(891): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#17 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#18 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\RedirectIfAuthenticated.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\RedirectIfAuthenticated->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#29 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#36 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#38 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#61 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#62 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#63 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#64 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\Foundation\\ViteManifestNotFoundException(code: 0): Vite manifest not found at: C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\public\\build/manifest.json at C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Vite.php:934)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Vite.php(384): Illuminate\\Foundation\\Vite->manifest('build')
#1 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\storage\\framework\\views\\97e03f7ec250738c76544cb8575cd71d.php(20): Illuminate\\Foundation\\Vite->__invoke(Object(Illuminate\\Support\\Collection))
#2 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Users\\\\<USER>\\Users\\En.Faizahmad\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#4 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(57): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\Users\\En.Faizahmad\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\Users\\En.Faizahmad\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\En.Faizahmad\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#8 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#9 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\storage\\framework\\views\\ca05b96c2e72f3e1cab73865fcb4cbe4.php(104): Illuminate\\View\\View->render()
#10 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Users\\\\<USER>\\Users\\En.Faizahmad\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#12 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(57): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\Users\\En.Faizahmad\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\Users\\En.Faizahmad\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\En.Faizahmad\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#16 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#17 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(78): Illuminate\\View\\View->render()
#18 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(34): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#19 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(924): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#20 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(891): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#21 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#22 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\RedirectIfAuthenticated.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\RedirectIfAuthenticated->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#33 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#40 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#42 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#65 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#66 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#67 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#68 C:\\Users\\<USER>\\Desktop\\project monogroph\\faiz-ahmad-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>