@extends('layouts.app')

@section('content')
<div class="row">
    <div class="col-md-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>جزئیات فروش #{{ $sale->id }}</h1>
            <a href="{{ route('sales.index') }}" class="btn btn-secondary">بازگشت به لیست</a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5>اقلام فروش</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>محصول</th>
                                <th>قیمت واحد</th>
                                <th>تعداد</th>
                                <th>قیمت کل</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($sale->saleItems as $item)
                            <tr>
                                <td>{{ $item->product->name }}</td>
                                <td>{{ number_format($item->unit_price) }} تومان</td>
                                <td>{{ $item->quantity }}</td>
                                <td>{{ number_format($item->total_price) }} تومان</td>
                            </tr>
                            @endforeach
                        </tbody>
                        <tfoot>
                            <tr class="table-dark">
                                <th colspan="3">مجموع کل:</th>
                                <th>{{ number_format($sale->total_amount) }} تومان</th>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5>اطلاعات فروش</h5>
            </div>
            <div class="card-body">
                <table class="table table-borderless">
                    <tr>
                        <td><strong>شماره فروش:</strong></td>
                        <td>#{{ $sale->id }}</td>
                    </tr>
                    <tr>
                        <td><strong>تاریخ فروش:</strong></td>
                        <td>{{ $sale->created_at->format('Y/m/d H:i') }}</td>
                    </tr>
                    <tr>
                        <td><strong>مشتری:</strong></td>
                        <td>{{ $sale->customer ? $sale->customer->name : 'مشتری ناشناس' }}</td>
                    </tr>
                    @if($sale->customer && $sale->customer->phone)
                    <tr>
                        <td><strong>شماره تماس:</strong></td>
                        <td>{{ $sale->customer->phone }}</td>
                    </tr>
                    @endif
                    <tr>
                        <td><strong>تعداد اقلام:</strong></td>
                        <td>{{ $sale->saleItems->count() }} قلم</td>
                    </tr>
                    <tr>
                        <td><strong>مبلغ کل:</strong></td>
                        <td><strong>{{ number_format($sale->total_amount) }} تومان</strong></td>
                    </tr>
                </table>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-body text-center">
                <h6>عملیات</h6>
                <button onclick="window.print()" class="btn btn-info btn-sm">چاپ فاکتور</button>
                <form action="{{ route('sales.destroy', $sale) }}" method="POST" class="d-inline mt-2" onsubmit="return confirm('آیا مطمئن هستید؟ این عمل موجودی محصولات را بازمی‌گرداند.')">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger btn-sm">حذف فروش</button>
                </form>
            </div>
        </div>
    </div>
</div>

<style>
@media print {
    .btn, .card-header, nav, .d-flex {
        display: none !important;
    }
    .card {
        border: none !important;
        box-shadow: none !important;
    }
}
</style>
@endsection
