<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Product;
use App\Models\Sale;
use App\Models\Customer;

class DashboardController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function index()
    {
        $totalProducts = Product::count();
        $totalSales = Sale::count();
        $totalCustomers = Customer::count();
        $totalRevenue = Sale::sum('total_amount');
        $lowStockProducts = Product::where('stock', '<=', 5)->get();
        $recentSales = Sale::with(['customer', 'saleItems.product'])->latest()->take(5)->get();

        return view('dashboard', compact(
            'totalProducts',
            'totalSales',
            'totalCustomers',
            'totalRevenue',
            'lowStockProducts',
            'recentSales'
        ));
    }
}
