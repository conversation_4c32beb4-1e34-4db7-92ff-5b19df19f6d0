<?php $__env->startSection('content'); ?>
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">داشبورد فروشگاه فیض احمد</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary">امروز</button>
        </div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card text-white bg-primary h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h2 class="card-title mb-2"><?php echo e($totalProducts); ?></h2>
                        <h5 class="card-text">تعداد محصولات</h5>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-box fa-3x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-3">
        <div class="card text-white bg-success h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h2 class="card-title mb-2"><?php echo e($totalSales); ?></h2>
                        <h5 class="card-text">تعداد فروش‌ها</h5>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-shopping-cart fa-3x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-3">
        <div class="card text-white bg-info h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h2 class="card-title mb-2"><?php echo e($totalCustomers); ?></h2>
                        <h5 class="card-text">تعداد مشتری‌ها</h5>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-users fa-3x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-3">
        <div class="card text-white bg-warning h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h2 class="card-title mb-2"><?php echo e(number_format($totalRevenue)); ?></h2>
                        <h6 class="card-text">تومان - کل درآمد</h6>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-money-bill fa-3x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">محصولات کم موجود</h5>
            </div>
            <div class="card-body">
                <?php if($lowStockProducts->count() > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>نام محصول</th>
                                    <th>موجودی</th>
                                    <th>قیمت</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $lowStockProducts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td><?php echo e($product->name); ?></td>
                                    <td><span class="badge bg-danger"><?php echo e($product->stock); ?></span></td>
                                    <td><?php echo e(number_format($product->price)); ?> تومان</td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <p class="text-muted">همه محصولات موجودی کافی دارند.</p>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">آخرین فروش‌ها</h5>
            </div>
            <div class="card-body">
                <?php if($recentSales->count() > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>شماره فروش</th>
                                    <th>مشتری</th>
                                    <th>مبلغ</th>
                                    <th>تاریخ</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $recentSales; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $sale): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td>#<?php echo e($sale->id); ?></td>
                                    <td><?php echo e($sale->customer ? $sale->customer->name : 'مشتری ناشناس'); ?></td>
                                    <td><?php echo e(number_format($sale->total_amount)); ?> تومان</td>
                                    <td><?php echo e($sale->created_at->format('Y/m/d H:i')); ?></td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <p class="text-muted">هنوز فروشی ثبت نشده است.</p>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-body text-center">
                <h5>عملیات سریع</h5>
                <a href="<?php echo e(route('products.create')); ?>" class="btn btn-primary me-2">افزودن محصول جدید</a>
                <a href="<?php echo e(route('sales.create')); ?>" class="btn btn-success me-2">ثبت فروش جدید</a>
                <a href="<?php echo e(route('customers.create')); ?>" class="btn btn-info">افزودن مشتری جدید</a>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\project monogroph\faiz-ahmad-shop\resources\views/dashboard.blade.php ENDPATH**/ ?>