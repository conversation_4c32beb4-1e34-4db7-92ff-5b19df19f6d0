@extends('layouts.app')

@section('content')
<div class="row">
    <div class="col-md-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>ثبت فروش جدید</h1>
            <a href="{{ route('sales.index') }}" class="btn btn-secondary">بازگشت به لیست</a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-body">
                <form action="{{ route('sales.store') }}" method="POST" id="saleForm">
                    @csrf
                    
                    <!-- اطلاعات مشتری -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h5>اطلاعات مشتری (اختیاری)</h5>
                            <div class="mb-3">
                                <label for="customer_name" class="form-label">نام مشتری</label>
                                <input type="text" class="form-control @error('customer_name') is-invalid @enderror" 
                                       id="customer_name" name="customer_name" value="{{ old('customer_name') }}">
                                @error('customer_name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="mb-3">
                                <label for="customer_phone" class="form-label">شماره تماس</label>
                                <input type="text" class="form-control @error('customer_phone') is-invalid @enderror" 
                                       id="customer_phone" name="customer_phone" value="{{ old('customer_phone') }}">
                                @error('customer_phone')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <!-- محصولات -->
                    <div class="mb-4">
                        <h5>محصولات</h5>
                        <div id="products-container">
                            <div class="product-row row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label">محصول</label>
                                    <select name="products[0][id]" class="form-select product-select" required>
                                        <option value="">انتخاب محصول</option>
                                        @foreach($products as $product)
                                            <option value="{{ $product->id }}" data-price="{{ $product->price }}" data-stock="{{ $product->stock }}">
                                                {{ $product->name }} (موجودی: {{ $product->stock }}) - {{ number_format($product->price) }} تومان
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">تعداد</label>
                                    <input type="number" name="products[0][quantity]" class="form-control quantity-input" min="1" required>
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">قیمت کل</label>
                                    <input type="text" class="form-control total-price" readonly>
                                </div>
                                <div class="col-md-1">
                                    <label class="form-label">&nbsp;</label>
                                    <button type="button" class="btn btn-danger remove-product d-block" style="display: none;">حذف</button>
                                </div>
                            </div>
                        </div>
                        
                        <button type="button" id="add-product" class="btn btn-success">افزودن محصول</button>
                    </div>

                    <!-- مجموع -->
                    <div class="row mb-4">
                        <div class="col-md-6 ms-auto">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h5>مجموع کل: <span id="grand-total">0</span> تومان</h5>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ route('sales.index') }}" class="btn btn-secondary me-md-2">انصراف</a>
                        <button type="submit" class="btn btn-primary">ثبت فروش</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    let productIndex = 1;
    
    function updateTotal() {
        let grandTotal = 0;
        document.querySelectorAll('.total-price').forEach(function(input) {
            const value = parseFloat(input.value.replace(/,/g, '')) || 0;
            grandTotal += value;
        });
        document.getElementById('grand-total').textContent = grandTotal.toLocaleString();
    }
    
    function addProductRow() {
        const container = document.getElementById('products-container');
        const newRow = container.querySelector('.product-row').cloneNode(true);
        
        // Update names and clear values
        newRow.querySelector('select').name = `products[${productIndex}][id]`;
        newRow.querySelector('select').value = '';
        newRow.querySelector('.quantity-input').name = `products[${productIndex}][quantity]`;
        newRow.querySelector('.quantity-input').value = '';
        newRow.querySelector('.total-price').value = '';
        newRow.querySelector('.remove-product').style.display = 'block';
        
        container.appendChild(newRow);
        productIndex++;
        
        // Show remove buttons if more than one row
        if (container.children.length > 1) {
            document.querySelectorAll('.remove-product').forEach(btn => btn.style.display = 'block');
        }
    }
    
    document.getElementById('add-product').addEventListener('click', addProductRow);
    
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('remove-product')) {
            const container = document.getElementById('products-container');
            if (container.children.length > 1) {
                e.target.closest('.product-row').remove();
                updateTotal();
                
                // Hide remove buttons if only one row left
                if (container.children.length === 1) {
                    document.querySelector('.remove-product').style.display = 'none';
                }
            }
        }
    });
    
    document.addEventListener('change', function(e) {
        if (e.target.classList.contains('product-select') || e.target.classList.contains('quantity-input')) {
            const row = e.target.closest('.product-row');
            const select = row.querySelector('.product-select');
            const quantityInput = row.querySelector('.quantity-input');
            const totalPriceInput = row.querySelector('.total-price');
            
            const selectedOption = select.options[select.selectedIndex];
            const price = parseFloat(selectedOption.dataset.price) || 0;
            const quantity = parseInt(quantityInput.value) || 0;
            const stock = parseInt(selectedOption.dataset.stock) || 0;
            
            if (quantity > stock) {
                alert('تعداد درخواستی بیش از موجودی است!');
                quantityInput.value = stock;
                return;
            }
            
            const total = price * quantity;
            totalPriceInput.value = total.toLocaleString();
            updateTotal();
        }
    });
});
</script>
@endsection
