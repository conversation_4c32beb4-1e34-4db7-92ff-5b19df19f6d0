@extends('layouts.app')

@section('content')
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">داشبورد فروشگاه فیض احمد</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary">امروز</button>
        </div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card text-white bg-primary h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h2 class="card-title mb-2">{{ $totalProducts }}</h2>
                        <h5 class="card-text">تعداد محصولات</h5>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-box fa-3x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-3">
        <div class="card text-white bg-success h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h2 class="card-title mb-2">{{ $totalSales }}</h2>
                        <h5 class="card-text">تعداد فروش‌ها</h5>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-shopping-cart fa-3x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-3">
        <div class="card text-white bg-info h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h2 class="card-title mb-2">{{ $totalCustomers }}</h2>
                        <h5 class="card-text">تعداد مشتری‌ها</h5>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-users fa-3x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-3">
        <div class="card text-white bg-warning h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h2 class="card-title mb-2">{{ number_format($totalRevenue) }}</h2>
                        <h6 class="card-text">تومان - کل درآمد</h6>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-money-bill fa-3x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">محصولات کم موجود</h5>
            </div>
            <div class="card-body">
                @if($lowStockProducts->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>نام محصول</th>
                                    <th>موجودی</th>
                                    <th>قیمت</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($lowStockProducts as $product)
                                <tr>
                                    <td>{{ $product->name }}</td>
                                    <td><span class="badge bg-danger">{{ $product->stock }}</span></td>
                                    <td>{{ number_format($product->price) }} تومان</td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <p class="text-muted">همه محصولات موجودی کافی دارند.</p>
                @endif
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">آخرین فروش‌ها</h5>
            </div>
            <div class="card-body">
                @if($recentSales->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>شماره فروش</th>
                                    <th>مشتری</th>
                                    <th>مبلغ</th>
                                    <th>تاریخ</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($recentSales as $sale)
                                <tr>
                                    <td>#{{ $sale->id }}</td>
                                    <td>{{ $sale->customer ? $sale->customer->name : 'مشتری ناشناس' }}</td>
                                    <td>{{ number_format($sale->total_amount) }} تومان</td>
                                    <td>{{ $sale->created_at->format('Y/m/d H:i') }}</td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <p class="text-muted">هنوز فروشی ثبت نشده است.</p>
                @endif
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-body text-center">
                <h5>عملیات سریع</h5>
                <a href="{{ route('products.create') }}" class="btn btn-primary me-2">افزودن محصول جدید</a>
                <a href="{{ route('sales.create') }}" class="btn btn-success me-2">ثبت فروش جدید</a>
                <a href="{{ route('customers.create') }}" class="btn btn-info">افزودن مشتری جدید</a>
            </div>
        </div>
    </div>
</div>
@endsection
