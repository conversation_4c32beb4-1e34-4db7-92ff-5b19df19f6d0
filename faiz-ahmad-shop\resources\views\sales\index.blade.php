@extends('layouts.app')

@section('content')
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">مدیریت فروش‌ها</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{{ route('sales.create') }}" class="btn btn-success btn-lg">
            <i class="fas fa-plus me-2"></i>
            ثبت فروش جدید
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-body">
                @if($sales->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>شماره فروش</th>
                                    <th>مشتری</th>
                                    <th>تعداد اقلام</th>
                                    <th>مبلغ کل</th>
                                    <th>تاریخ فروش</th>
                                    <th>عملیات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($sales as $sale)
                                <tr>
                                    <td>#{{ $sale->id }}</td>
                                    <td>{{ $sale->customer ? $sale->customer->name : 'مشتری ناشناس' }}</td>
                                    <td>{{ $sale->saleItems->count() }} قلم</td>
                                    <td>{{ number_format($sale->total_amount) }} تومان</td>
                                    <td>{{ $sale->created_at->format('Y/m/d H:i') }}</td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ route('sales.show', $sale) }}" class="btn btn-sm btn-info">جزئیات</a>
                                            <form action="{{ route('sales.destroy', $sale) }}" method="POST" class="d-inline" onsubmit="return confirm('آیا مطمئن هستید؟ این عمل موجودی محصولات را بازمی‌گرداند.')">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="btn btn-sm btn-danger">حذف</button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="mt-3">
                        <div class="row">
                            <div class="col-md-6">
                                <strong>تعداد کل فروش‌ها: {{ $sales->count() }}</strong>
                            </div>
                            <div class="col-md-6 text-end">
                                <strong>مجموع درآمد: {{ number_format($sales->sum('total_amount')) }} تومان</strong>
                            </div>
                        </div>
                    </div>
                @else
                    <div class="text-center py-4">
                        <p class="text-muted">هیچ فروشی ثبت نشده است.</p>
                        <a href="{{ route('sales.create') }}" class="btn btn-primary">ثبت اولین فروش</a>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection
