@extends('layouts.app')

@section('content')
<div class="row">
    <div class="col-md-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>مدیریت مشتری‌ها</h1>
            <a href="{{ route('customers.create') }}" class="btn btn-primary">افزودن مشتری جدید</a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-body">
                @if($customers->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>شناسه</th>
                                    <th>نام مشتری</th>
                                    <th>شماره تماس</th>
                                    <th>تعداد خریدها</th>
                                    <th>تاریخ ثبت</th>
                                    <th>عملیات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($customers as $customer)
                                <tr>
                                    <td>{{ $customer->id }}</td>
                                    <td>{{ $customer->name }}</td>
                                    <td>{{ $customer->phone ?: 'ثبت نشده' }}</td>
                                    <td>
                                        @if($customer->sales_count > 0)
                                            <span class="badge bg-success">{{ $customer->sales_count }} خرید</span>
                                        @else
                                            <span class="badge bg-secondary">بدون خرید</span>
                                        @endif
                                    </td>
                                    <td>{{ $customer->created_at->format('Y/m/d') }}</td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ route('customers.show', $customer) }}" class="btn btn-sm btn-info">نمایش</a>
                                            <a href="{{ route('customers.edit', $customer) }}" class="btn btn-sm btn-warning">ویرایش</a>
                                            <form action="{{ route('customers.destroy', $customer) }}" method="POST" class="d-inline" onsubmit="return confirm('آیا مطمئن هستید؟')">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="btn btn-sm btn-danger">حذف</button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <div class="text-center py-4">
                        <p class="text-muted">هیچ مشتری‌ای یافت نشد.</p>
                        <a href="{{ route('customers.create') }}" class="btn btn-primary">افزودن اولین مشتری</a>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection
